import type { AppointmentData } from "../store/appointmentBookingSlice"

export const bookAppointment = async (appointmentData: AppointmentData) => {
  const response = await fetch("/api/booking/book", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(appointmentData),
  })

  if (!response.ok) {
    let errorMessage = "Failed to book appointment"

    try {
      const errorData = await response.json() as { message?: string; error?: string }
      // Use the server's error message if available
      if (errorData.message) {
        errorMessage = errorData.message
      } else if (errorData.error) {
        errorMessage = errorData.error
      }
    } catch (parseError) {
      // If we can't parse the error response, use the status text
      errorMessage = response.statusText || errorMessage
    }

    throw new Error(errorMessage)
  }

  return response.json()
}
